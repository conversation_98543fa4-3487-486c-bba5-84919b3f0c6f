import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inset_box_shadow/flutter_inset_box_shadow.dart';
import 'package:phoenix/features/common/broker_account_strategy_data.dart';
import 'package:phoenix/features/orders_state/model/unified_order_data.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/screens/orders/order_form_bottom_sheet.dart';
import 'package:phoenix/screens/orders/order_type.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/util_functions.dart';
import 'package:phoenix/widgets/broker_avatar/broker_avatar.dart';
import 'package:phoenix/widgets/custom/dismissible/zen_dismissible.dart';
import 'package:phoenix/widgets/tile/orders_state_tile/order_status_indicator.dart';
import 'package:phoenix/widgets/tile/orders_state_tile/order_type_pill.dart';
import 'package:phoenix/widgets/tile/orders_state_tile/timeline_item_widget.dart';

//Unified order state is to show parent child
class UnifiedOrderStateChild extends StatefulWidget {
  const UnifiedOrderStateChild({
    super.key,
    required this.isOrderOpen,
    required this.data,
    required this.animationController,
    required this.brokerAccountStrategyMapData,
    this.prices,
  });

  final UnifiedOrderData data;
  //Order form animation controller
  final AnimationController animationController;

  final bool isOrderOpen;
  final double? prices;
  final BrokerAccountStrategyData? brokerAccountStrategyMapData;

  @override
  State<UnifiedOrderStateChild> createState() => _UnifiedOrderState();
}

class _UnifiedOrderState extends State<UnifiedOrderStateChild>
    with TickerProviderStateMixin {
  bool isTileOpen = false;
  @override
  Widget build(BuildContext context) {
    // Handle empty orderFillState array when status is in_progress
    final bool hasOrderFillState = widget.data.orderFillState.isNotEmpty;
    final OrderFillState? firstOrderFill = hasOrderFillState ? widget.data.orderFillState[0] : null;

    final String transactionType = firstOrderFill?.transactionType ?? "BUY";
    final Color transactionTypeColor = transactionType == "BUY"
        ? ThemeConstants.tileGreenColor
        : ThemeConstants.titleRedColor;
    final Color transactionTypeBgColor = transactionType == "BUY"
        ? ThemeConstants.buyBackgroundColor
        : ThemeConstants.sellBackgroudColor;

    String formattedTime = firstOrderFill?.exchangeTimestamp == null
        ? "--"
        : "${firstOrderFill!.exchangeTimestamp!.hour}:${firstOrderFill.exchangeTimestamp!.minute}:${firstOrderFill.exchangeTimestamp!.second}";

    String overAllStatus = widget.data.status;
    String tradingSymbol = widget.data.tradingSymbol;
    bool isParentChildOrder =
        widget.data.orderFillState.length > 1 ? true : false;
    
   

    List<TimelineItem> timelineItems = hasOrderFillState
        ? widget.data.orderFillState.map((order) {
            return TimelineItem(
              title: order.orderStatus,
              dateTime: order.exchangeTimestamp,
              isCompleted: order.orderStatus == "COMPLETED",
              transactionType: order.transactionType,
              orderType: order.orderType,
              internalOrderId: order.internalOrderId,
              totalFilledQuantity: order.totalFilledQuantity,
              totalQuantity: order.totalQuantity,
            );
          }).toList()
        : <TimelineItem>[];

    if (timelineItems.isNotEmpty) {
      timelineItems.sort(
        (a, b) => a.internalOrderId.compareTo(b.internalOrderId),
      );
    }

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (isParentChildOrder) {
          setState(() {
            isTileOpen = !isTileOpen;
          });
        }
      },
      child: ZenDismissible(
        //Unique key for each order
        key: Key(
          widget.data.zenOrderId.toString(),
        ),
        dismissThresholds: const {
          ZenDismissDirection.endToStart: 0.20,
          ZenDismissDirection.startToEnd: 0.20,
        },
        background: slideDeleteBackground(),
        secondaryBackground: slideModifyBackground(),
        direction: (overAllStatus == "OPEN" ||
                overAllStatus == "UPDATE" ||
                overAllStatus == "TRIGGER_PENDING" ||
                overAllStatus == "PENDING")
            ? DismissDirection.horizontal
            : DismissDirection.none,
        movementDuration: const Duration(milliseconds: 200),
        confirmDismiss: swipeHandler,
        child: BlocBuilder<ThemeBloc, ThemeState>(
          builder: (context, themeState) {
            return Container(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 14),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                color: AppTheme.surfaceColor(themeState.isDarkMode),
                boxShadow: themeState.isDarkMode
                    ? ThemeConstants.neomorpicShadow
                    : [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.2),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
              ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Left Column
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      //Transaction Type Buy or Sell
                      Row(
                        children: [
                          Container(
                            color: transactionTypeBgColor,
                            height: 20,
                            width: 45,
                            alignment: Alignment.center,
                            child: Text(
                              transactionType.toCapitalized,
                              style: TextStyle(
                                color: transactionTypeColor,
                                fontSize: 15,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
                          const SizedBox(width: 4),
                          if (isParentChildOrder)...[
                            ImageIcon(
                              AssetImage("images/branch.png"),
                              color: ThemeConstants.blue,
                              size: 18,
                            ),
                            const SizedBox(width: 4),
                          ],
                          OrderTypePill(orderType: widget.data.orderType)
                        ],
                      ),
                      const SizedBox(height: 4),

                      //Trading symbol
                      Row(
                        children: [
                          Text(
                            tradingSymbol,
                            style: TextStyle(
                              color: AppTheme.textPrimary(themeState.isDarkMode),
                              fontSize: 15,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          const SizedBox(width: 4),
                          if (widget.data.positionCompKey
                              .broker.isNotEmpty)
                            BrokerAvatar(
                              brokerName: widget.data.positionCompKey.broker,
                            ),
                        ],
                      ),

                      // Strategy
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const ImageIcon(
                            AssetImage("images/tile-generic/strategy.png"),
                            color: Colors.blue,
                            size: 15,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            widget.brokerAccountStrategyMapData?.strategyName ??
                                "N/A",
                            style: TextStyle(
                              color: AppTheme.textSecondary(themeState.isDarkMode),
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 19),

                      // LTP  //Avg
                      RichText(
                        text: TextSpan(
                          children: [
                            widget.isOrderOpen
                                ? TextSpan(
                                    text: "LTP ",
                                    style: TextStyle(
                                      color: AppTheme.textSecondary(themeState.isDarkMode),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  )
                                : TextSpan(
                                    text: "Avg ",
                                    style: TextStyle(
                                      color: AppTheme.textSecondary(themeState.isDarkMode),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                            widget.isOrderOpen
                                ? TextSpan(
                                    text: (widget.prices == null ||
                                            widget.prices == 0.0)
                                        ? "--"
                                        : UtilFunctions.formatIndianCurrency(
                                            widget.prices as double),
                                    style: TextStyle(
                                      color: AppTheme.textSecondary(themeState.isDarkMode),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  )
                                : TextSpan(
                                    text: UtilFunctions.formatIndianCurrency(
                                        widget.data.orderFillState[0].averagePrice),
                                    style: TextStyle(
                                      color: AppTheme.textSecondary(themeState.isDarkMode),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  // Right Column
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Row(
                        children: [
                          const ImageIcon(
                            AssetImage("images/time.png"),
                            color: Color(0xffC5C5C5),
                            size: 12,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            formattedTime,
                            style: TextStyle(
                              color: AppTheme.textSecondary(themeState.isDarkMode),
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        "CNC",
                        style: TextStyle(
                          color: AppTheme.textSecondary(themeState.isDarkMode),
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      const SizedBox(height: 4),
                      OrderStatusIndicator(status: overAllStatus),
                      const SizedBox(height: 4),
                      if(!isParentChildOrder && firstOrderFill != null) Row(
                        children: [
                          const ImageIcon(
                            AssetImage("images/tile-generic/qty_icon.png"),
                            color: Color(0xff338AFF),
                            size: 12,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            "${widget.data.orderFillState[0].totalFilledQuantity}/${widget.data.orderFillState[0].totalQuantity}",
                            style: TextStyle(
                              color: AppTheme.textSecondary(themeState.isDarkMode),
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),

              // Order history time line
              if (isParentChildOrder)
                AnimatedSize(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                  child: isTileOpen
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            const SizedBox(height: 8),
                            OrderTimelineWidget(
                              timelineItems: timelineItems,
                              overAllStatus: overAllStatus,
                            ),
                          ],
                        )
                      : const SizedBox.shrink(),
                ),
            ],
          ),
            );
          },
        ),
      ),
    );
  }

  Widget slideDeleteBackground() {
    return Container(
      decoration: BoxDecoration(
        color: ThemeConstants.modifyOptionBgColor,
        borderRadius: const BorderRadius.horizontal(
          left: Radius.circular(20),
          right: Radius.circular(20),
        ),
        boxShadow: ThemeConstants.neomorpicShadow2,
      ),
      child: const Align(
        alignment: Alignment.centerRight,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            SizedBox(
              width: 20,
            ),
            Text(
              " Delete",
              style: TextStyle(
                color: Color(0xffCDCDCD),
                fontWeight: FontWeight.w800,
                fontSize: 15,
              ),
              textAlign: TextAlign.left,
            ),
          ],
        ),
      ),
    );
  }

//sell
  Widget slideModifyBackground() {
    return Container(
      decoration: BoxDecoration(
        color: ThemeConstants.modifyOptionBgColor,
        borderRadius: const BorderRadius.horizontal(
          left: Radius.circular(20),
          right: Radius.circular(20),
        ),
        boxShadow: ThemeConstants.neomorpicShadow2,
      ),
      child: const Align(
        alignment: Alignment.centerLeft,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: <Widget>[
            Text(
              " Modify",
              style: TextStyle(
                color: Color(0xffCDCDCD),
                fontWeight: FontWeight.w800,
                fontSize: 15,
              ),
              textAlign: TextAlign.right,
            ),
            SizedBox(
              width: 20,
            ),
          ],
        ),
      ),
    );
  }

  Future<bool?> swipeHandler(DismissDirection direction) async {
    FormOpenOrderType action = direction == DismissDirection.startToEnd
        ? FormOpenOrderType.delete
        : FormOpenOrderType.edit;

    showOrderForm(
      context,
      widget.animationController,
      action,
      widget.data, //need to change
    );

    // Return false to prevent actual dismissal
    return false;
  }
}
