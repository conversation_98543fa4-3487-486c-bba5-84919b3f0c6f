// theme_constants.dart
import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;

import 'package:flutter_inset_box_shadow/flutter_inset_box_shadow.dart';
//For this to work we need to do some changes in the local files downloaded.
///C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_inset_box_shadow-1.0.8\lib\src\box_shadow.dart
/// Make below changes here
/// @override
///  int get hashCode =>
///    Object.hash(color, offset, blurRadius, spreadRadius, blurStyle, inset);

class ThemeConstants {
  // Legacy colors for backward compatibility - Dark theme colors
  static const Color backgroundColor = Color(0xff24272C);
  static const Color blue = Color(0xff338AFF);
  static const Color tileIconColor = Color(0xffCDCDCD);
  static const Color tileGreenColor = Color(0xff00C400);
  static const Color titleRedColor = Color(0xFFD64242);
  static const Color zenWhite = Color(0xffCDCDCD);
  static const Color zenBlack = Color(0xff383838);
  static const Color zenBlack1 = Color(0xff0D0B0B);
  static const Color zenGrey = Color(0xffc5c5c5);

  // Dynamic theme support - these methods return colors based on theme mode
  static Color getBackgroundColor(bool isDarkMode) => 
      isDarkMode ? const Color(0xff24272C) : const Color(0xffF8F9FA);
  
  static Color getTextColor(bool isDarkMode) => 
      isDarkMode ? const Color(0xffCDCDCD) : const Color(0xff2C3E50);
  
  static Color getSurfaceColor(bool isDarkMode) => 
      isDarkMode ? const Color(0xff2D3035) : const Color(0xffFFFFFF);
  
  static Color getBorderColor(bool isDarkMode) => 
      isDarkMode ? const Color(0xff404449) : const Color(0xffE9ECEF);

  static const String appBarLogo = "images/app_bar_logo.png";
  static const TextStyle textStyle = TextStyle(
      fontWeight: FontWeight.w400, fontSize: 12, color: Color(0xffCDCDCD));

  static const Color floatingActionButtonColor = Color(0xff368cff);

  //Neumorphism
  //Shadow decorations
  static List<BoxShadow> neomorpicShadow = [
    BoxShadow(
      color: Color.fromARGB(255, 62, 66, 75),
      // color: Color(0xFF1D1F23),
      offset: Offset(-10, -10),
      blurRadius: 20,
      inset: true,
    ),
    BoxShadow(
      color: Color.fromARGB(255, 21, 23, 26),
      // color:  Color(0xFF2A2D33),
      offset: Offset(10, 10),
      blurRadius: 20,
      inset: true,
    ),
  ];

  static const List<BoxShadow> innerShadow = [
    BoxShadow(
      //color: Color.fromARGB(255, 62, 66, 75),
      color: Color(0xFF464646),
      offset: Offset(-10, -10),
      blurRadius: 20,
      inset: true,
    ),
  ];

  //Neumorphism
  //Shadow decorations
  //For tile backgorund
  static const List<BoxShadow> neomorpicShadow2 = [
    BoxShadow(
      color: Color.fromARGB(255, 161, 161, 161),
      // color: Color(0xFF1D1F23),
      offset: Offset(-10, -10),
      blurRadius: 20,
      inset: true,
    ),
    BoxShadow(
      color: Color.fromARGB(255, 12, 12, 12),
      // color:  Color(0xFF2A2D33),
      offset: Offset(10, 10),
      blurRadius: 20,
      inset: true,
    ),
  ];

  //blue neom
  static List<BoxShadow> neomorpicShadowblue = [
    BoxShadow(
      color: Color.fromARGB(255, 62, 66, 75),
     
      offset: Offset(-10, -10),
      blurRadius: 20,
      inset: true,
    ),
    BoxShadow(
      color: Color.fromARGB(255, 21, 23, 26),
      // color:  Color(0xFF2A2D33),
      offset: Offset(10, 10),
      blurRadius: 20,
      inset: true,
    ),
  ];

  //Toast Message colors
  static const Color toastSuccessColor = Color(0xff2F5639);
  static const Color toastFailedColor = Color(0xff522A2A);
  static const Color toastFillMandatoryFeildsColor = Color(0xff6D6F39);

  //OrderState colors
  static Color buyBackgroundColor = Color(0xff028824).withOpacity(0.25);
  static Color sellBackgroudColor = Color(0xffD64242).withOpacity(0.25);
  static Color modifyOptionBgColor = const Color.fromARGB(255, 72, 78, 88);


  //Positions tile buy sell colors
  static Color buySlideOptionColor = Color(0xff028824);
  static Color sellSlideOptionColor = Color(0xffD64242);

  static Color black = Color(0xff000000);
  static Color closedPositionBackgroundColor =
      Color(0xff24272C).withOpacity(0.5);

  //Text element
  static Widget zenText(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 15,
        fontFamily: 'CupertinoSystemText',
        color: Colors.white,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  //Side Drawer Colors
  static const Color bgSideDrawer = backgroundColor;

  static const TextStyle sdTextStyle = TextStyle(
    fontFamily: 'Roboto',
    fontWeight: FontWeight.w500,
    fontSize: 20,
    color: Color(0xffCDCDCD),
  );

  //Portfolio net worth colors - These remain universal
  static const Color netWorthGreenColor = Color(0xff00C400);
  static const Color netWorthRedColor = Color(0xffFF0000);
  static const Color netWorthTextRedColor = Color(0xffD64242);
  static const Color netWorthHeaderColor = Color(0xffCDCDCD);
  static const Color netWorthAmountColor = Color(0xffA2A2A2);
  
  // Dynamic net worth colors for light mode
  static Color getNetWorthHeaderColor(bool isDarkMode) => 
      isDarkMode ? const Color(0xffCDCDCD) : const Color(0xff2C3E50);
  
  static Color getNetWorthAmountColor(bool isDarkMode) => 
      isDarkMode ? const Color(0xffA2A2A2) : const Color(0xff6C757D);
  
}
