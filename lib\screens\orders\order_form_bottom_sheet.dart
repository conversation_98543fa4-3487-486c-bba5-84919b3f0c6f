import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/authentication/model/credentials_model.dart';
import 'package:phoenix/features/broker_account_strategy_selection/bloc/broker_account_strategy_selection_bloc.dart';
import 'package:phoenix/features/latest_zen_order_state/bloc/latest_zen_order_state_bloc.dart';
import 'package:phoenix/features/latest_zen_order_state/model/latest_order.dart';
import 'package:phoenix/features/orders/bloc/orders_bloc.dart';
import 'package:phoenix/features/orders/model/latest_order_type_model.dart';
import 'package:phoenix/features/orders/model/order_form_model.dart';
import 'package:phoenix/features/orders/model/order_form_validator.dart';
import 'package:phoenix/features/orders/model/position_order_data.dart';
import 'package:phoenix/features/orders/model/spider_order_data.dart';
import 'package:phoenix/features/orders_state/bloc/orders_state_bloc.dart';
import 'package:phoenix/features/orders_state/model/unified_order_data.dart';
import 'package:phoenix/features/security_list/bloc/security_list_bloc.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';
import 'package:phoenix/managers/api/api_response_manager.dart';
import 'package:phoenix/screens/orders/cancel_order_dialog.dart';
import 'package:phoenix/screens/orders/order_type.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/util_functions.dart';
import 'package:phoenix/widgets/animated_icons/spinning_icon_button.dart';
import 'package:phoenix/widgets/circular_loader.dart';
import 'package:phoenix/widgets/custom_radio_button/custom_radio_widget.dart';
import 'package:phoenix/widgets/order_form/accounts_widget.dart';
import 'package:phoenix/widgets/order_form/charges_bar/charges_bar.dart';
import 'package:phoenix/widgets/order_form/security_list_dropdown/custom_searchable_dropdown2.dart';
import 'package:phoenix/widgets/order_form/disabled_drop_down.dart';
import 'package:phoenix/widgets/order_form/exchange_selector2.dart';
import 'package:phoenix/widgets/order_form/header.dart';
import 'package:phoenix/widgets/order_form/input_box.dart';
import 'package:phoenix/widgets/order_form/line_bar.dart';
import 'package:phoenix/widgets/order_form/market_limit_toggler.dart';
import 'package:phoenix/widgets/order_form/order_form_margin_viewer.dart';
import 'package:phoenix/widgets/order_form/selection_drop_down.dart';
import 'package:phoenix/widgets/order_form/toggler_with_shadow.dart';
import 'package:phoenix/widgets/order_form/m_l_toggler.dart';
import 'dart:convert';
import 'package:phoenix/utils/http_service.dart';
import 'package:phoenix/widgets/price_input_formatter.dart';

import '../../utils/api_path.dart';

void showOrderForm(
    BuildContext context,
    AnimationController controller,
    FormOpenOrderType orderType,
    UnifiedOrderData? latestOrderData // Pass the order ID for fetching the data
    ) {
  int? clientId;
  final authData = context.read<AuthBloc>().state;

  if (authData is AuthAuthenticated) {
    clientId = authData.credentialsModel.clientId;
  } else if (authData is AuthUnauthenticated) {
    Navigator.pushReplacementNamed(context, '/login');
  }

  if (orderType == FormOpenOrderType.edit) {
    final latestOrderBloc = BlocProvider.of<LatestZenOrderStateBloc>(context);

    // Dispatch the fetch event
    latestOrderBloc.add(LatestZenOrderStateFetchEvent(
      latestOrderData!.zenOrderId,
      clientId!,
    ));

    //EDIT ORDER
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      transitionAnimationController: controller,
      builder: (modalContext) {
        return BlocBuilder<LatestZenOrderStateBloc, LatestZenOrderStateState>(
          builder: (context, state) {
            if (state is LatestZenOrderStateLoading) {
              return const Center(
                child: CircularLoader(), // Show loader while fetching
              );
            } else if (state is LatestZenOrderStateLoaded) {
              return SingleChildScrollView(
                reverse: true,
                child: OrderFormSheet(
                  latestOrderData: state.data,
                  openOrderType: FormOpenOrderType.edit,
                ), // Pass fetched order data
              );
            } else if (state is LatestZenOrderStateError) {
              return Center(
                child: BlocBuilder<ThemeBloc, ThemeState>(
                  builder: (context, themeState) {
                    return Text(
                      "Error: ${state.error}",
                      style: TextStyle(
                        fontSize: 15,
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        overflow: TextOverflow.ellipsis,
                      ),
                    );
                  },
                ),
              );
            } else {
              return Center(
                child: BlocBuilder<ThemeBloc, ThemeState>(
                  builder: (context, themeState) {
                    return Text(
                      "No order data available",
                      style: TextStyle(
                        fontSize: 15,
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        overflow: TextOverflow.ellipsis,
                      ),
                    );
                  },
                ),
              );
            }
          },
        );
      },
    ).whenComplete(() {
      debugPrint("Edit Order form closed");
      if (context.mounted) {
        // Get current orders state and update WebSocket subscriptions
        final ordersState = context.read<OrdersStateBloc>().state;
        if (ordersState is OrdersStateLoaded) {
          final openData = ordersState.data
              .where((order) => [
                    "update",
                    "trigger_pending",
                    "open",
                    "in_progress",
                    "pending",
                  ].contains(order.status.toLowerCase()))
              .toList();
          Set<int> stockIds =
              openData.map((e) => e.positionCompKey.zenSecId).toSet();

          debugPrint(
              "🔄 Updating WebSocket subscriptions for open orders - ${stockIds.length} stocks");
          debugPrint(openData.toString());
          context
              .read<WebSocketBloc>()
              .add(WebSocketSelectMultipleStocks(stockIds.toList()));
        }
      }
    });
  } else if (orderType == FormOpenOrderType.delete) {
    final latestOrderBloc = BlocProvider.of<LatestZenOrderStateBloc>(context);

    // Dispatch the fetch event
    latestOrderBloc.add(LatestZenOrderStateFetchEvent(
      latestOrderData!.zenOrderId,
      clientId!,
    ));

    showDialog(
      context: context,
      builder: (context) =>
          BlocBuilder<LatestZenOrderStateBloc, LatestZenOrderStateState>(
        builder: (context, state) {
          if (state is LatestZenOrderStateLoaded) {
            debugPrint("Latest Order Data: ${state.data}");
            return CancelOrderDialog(
              onConfirm: () {
                OrderFormModel data = OrderFormModel(
                  clientId: state.data.positionCompKey.clientId,
                  accountId: state.data.positionCompKey.accountId,
                  strategyId: state.data.positionCompKey.strategyId,
                  broker: state.data.positionCompKey.broker,
                  exchange: state.data.exchange,
                  transactionType: state.data.transactionType,
                  quantity: state.data.quantity,
                  product: state.data.product,
                  validity: state.data.validity,
                  methodType: "DELETE",
                  zenId: state.data.positionCompKey.zenSecId,
                  zenOrderId: state.data.zenOrderId,
                );
                BlocProvider.of<OrdersBloc>(context).add(PlaceOrderEvent(data));
              },
            );
          }
          return const Center(child: CircularLoader());
        },
      ),
    );
  } else {
    BlocProvider.of<WebSocketBloc>(context).add(WebSocketSelectStock(null));
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      transitionAnimationController: controller,
      builder: (modalContext) {
        return SingleChildScrollView(
          reverse: true,
          child: OrderFormSheet(), // Normal modal without order data
        );
      },
    ).whenComplete(() {
      debugPrint("Normal Order form closed");
      if (context.mounted) {
        // Get current orders state and update WebSocket subscriptions
        BlocProvider.of<WebSocketBloc>(context).add(WebSocketSelectStock(null));
        final ordersState = context.read<OrdersStateBloc>().state;
        if (ordersState is OrdersStateLoaded) {
          final openData = ordersState.data
              .where((order) => [
                    "update",
                    "trigger_pending",
                    "open",
                    "in_progress",
                    "pending"
                  ].contains(order.status.toLowerCase()))
              .toList();
          Set<int> stockIds =
              openData.map((e) => e.positionCompKey.zenSecId).toSet();
          debugPrint(
              "🔄 Updating WebSocket subscriptions for open orders - ${stockIds.length} stocks");
          context
              .read<WebSocketBloc>()
              .add(WebSocketSelectMultipleStocks(stockIds.toList()));
        }
      }
    });
  }
}

void hideOrderForm(BuildContext context) {
  if (Navigator.of(context, rootNavigator: true).canPop()) {
    Navigator.of(context, rootNavigator: true).pop();
  }
}

class OrderFormSheet extends StatefulWidget {
  final LatestOrder? latestOrderData;
  final FormOpenOrderType openOrderType;
  final SpiderOrderData? spiderMetaData;
  final PositionOrderData? positionMetaData;

  const OrderFormSheet({
    super.key,
    this.latestOrderData,
    this.spiderMetaData,
    this.positionMetaData,
    this.openOrderType = FormOpenOrderType.create,
  });

  @override
  _OrderFormSheetState createState() => _OrderFormSheetState();
}

class _OrderFormSheetState extends State<OrderFormSheet>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();

  bool isNSE = true;
  String selectedExchangeValue = "NSE";
  bool isMarket = true;

  bool isEQ = true;
  // Current selected instrument type
  String _selectedInstrument = 'EQ'; // Default is EQ
  // Map for product types based on instrument type
  final Map<String, List<String>> _productTypes = {
    'EQ': ['CNC', 'MIS'],
    'F&O': ['NRML', 'MIS'],
  };

  // Currently selected product type
  String _selectedProduct = 'CNC'; // Default product for EQ

  final List<String> eqProductTypes = ["CNC", "MIS"];
  final List<String> foProductTypes = ["NRML", "MIS"];

  bool isCNC = true;

  bool isNRML = false;

  bool isStoplossEnabled = false;

  bool isTrailingStoplossEnabled = false;

  //Market or Limit
  String stopLossType = 'M';

  //Seleced Securitiy Value;
  SecurityModel? selectedValue;

  final TextEditingController textEditingController = TextEditingController();

  String? selectedBroker;

  // State for selected account ID
  String? selectedAccountId;

  // State for selected Strategy ID
  String? selectedStrategyId;

  //stock search controller
  final TextEditingController stockSearchController = TextEditingController();
  //Quantity
  int quantity = 0;
  final TextEditingController quantityController = TextEditingController();

  //Lots
  int lots = 1;
  int lotSize = 1;
  final TextEditingController lotsController = TextEditingController();

  //Price
  double? price;
  final TextEditingController priceController = TextEditingController();

  //price form the rest api
  double? defaultLtpPrice;

  //LimitPrice
  double? limitPrice = null;
  final TextEditingController limitPriceController = TextEditingController();

  //Trigger Price
  double? triggerPrice = null;
  final TextEditingController triggerPriceController = TextEditingController();

  //Stoploss price
  double? stopLossPrice = null;
  final TextEditingController stopLossPriceController = TextEditingController();

  //Activation points
  double? activationPoints = null;
  final TextEditingController activationPointsController =
      TextEditingController();

  //Order type BUY or SELL
  String? actionType;

  //View Controller
  //For statergy account broker selections
  bool isExpanded = false;

  //View Controller
  //For quantity account wise
  bool viewQuantity = false;

  late final AnimationController _controller;
  late final Animation<double> _animation;

  ///Refresh animation spinner
  late final AnimationController _formRefreshController;

  ///order form bottom sheet animation
  late final AnimationController _formSheetAnimeController;

  final ValueNotifier<bool> isSelectedStockValid = ValueNotifier<bool>(true);
  final ValueNotifier<bool> isQuantityValid = ValueNotifier<bool>(true);
  final ValueNotifier<bool> isLotsValid = ValueNotifier<bool>(true);
  final ValueNotifier<bool> isPriceValid = ValueNotifier<bool>(true);
  final ValueNotifier<bool> isActivationPointsValid = ValueNotifier<bool>(true);
  final ValueNotifier<bool> isLimitPriceValid = ValueNotifier<bool>(true);
  final ValueNotifier<bool> isTriggerPriceValid = ValueNotifier<bool>(true);
  final ValueNotifier<bool> isStoplossPricePriceValid =
      ValueNotifier<bool>(true);

  //final  ValueNotifier<bool> isSelectedStockValid = ValueNotifier<bool>(true);
  //this code it to trak the stock selection section and add extra padding for that
  final FocusNode _stockSelectionFieldFocusNode = FocusNode();

  ///Configuring Order form data
  ///
  String orderTitle = "";
  bool isShowBasedOnOrderType = true;
  String latestBrokerName = '';
  String latestAccountName = '';
  String latestStrategyName = '';
  LatestOrderTypeModel? latestOrderType;

  @override
  void initState() {
    super.initState();
    debugPrint('Init ran');
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = Tween(begin: 0.0, end: .5).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOut,
      ),
    );

    ///Clearing multiple stock selection stream
    context.read<WebSocketBloc>().add(WebSocketSelectStock(null));

    ///multiple
    BlocProvider.of<WebSocketBloc>(context).add(
      WebSocketClearSelectedStocks(),
    );

    debugPrint("clearing websocket subscription to null");

    ///Refresh animation spinner
    _formRefreshController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _stockSelectionFieldFocusNode.addListener(() {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {});
        }
      });
    });

    ///Order form pop up Animation
    _formSheetAnimeController = BottomSheet.createAnimationController(this);
    _formSheetAnimeController.duration = Duration(seconds: 3);

    // Determine form title based on order type
    switch (widget.openOrderType) {
      case FormOpenOrderType.create:
        orderTitle = "Create Order";
        debugPrint("👽 Create Order");
        break;
      case FormOpenOrderType.delete:
        orderTitle = "Delete Order";
        debugPrint("👽 Delete Order");
        break;
      case FormOpenOrderType.fromPosition:
        orderTitle = "Create Order";
        debugPrint("👽 From Positions Create Order");
        _initializePositionData();
        break;
      case FormOpenOrderType.spider:
        orderTitle = "Create Order";
        debugPrint("🕷️ Create Order");
        _initializeSpiderData();
        break;
      case FormOpenOrderType.buy:
        orderTitle = "Buy Order";
        debugPrint("🟢 Buy Order");
        actionType = "BUY";
        _initializeSpiderData();
        break;
      case FormOpenOrderType.sell:
        orderTitle = "Sell Order";
        debugPrint("🔴 Sell Order");
        actionType = "SELL";
        _initializeSpiderData();
        break;
      case FormOpenOrderType.edit:
        _initializeEditData();
        break;
      case FormOpenOrderType.view:
        orderTitle = "View Order";
        debugPrint("👽 View Order");
        break;
    }
  }

  // New method to handle position data initialization
  void _initializePositionData() {
    BlocProvider.of<WebSocketBloc>(context).add(
      WebSocketSelectStock(
        widget.positionMetaData!.zenId.toString(),
      ),
    );

    _fetchLtpPrice(widget.positionMetaData!.zenId);

    //Accessing the security list to find the positions zenId
    //need to optimize
    final secListState = context.read<SecurityListBloc>().state;

    if (secListState is SecurityListLoaded) {
      selectedValue = secListState.equityList.firstWhere(
        (data) => data.zenId == widget.positionMetaData!.zenId,
        orElse: () => secListState.featuresList.firstWhere(
          (data) => data.zenId == widget.positionMetaData!.zenId,
          orElse: () {
            debugPrint(
              "🕷️ Data not found in both equityList and featuresList",
            );
            return SecurityModel(
              zenId: 0,
              tradingSymbol: "Not Found",
              strike: 0.0,
              exchanges: ["NSE"],
              lotSize: 1,
              instrumentType: "EQ",
              expiryType: "null",
              expiry: "null",
              name: "Not Found",
            );
          },
        ),
      );
    }
    if (selectedValue != null) {
      debugPrint(selectedValue.toString());
      quantity = widget.positionMetaData!.positionData.position.abs();
      if (selectedValue!.instrumentType == "EQ") {
        isEQ = true;
        _selectedInstrument = 'EQ';
        _selectedProduct = "CNC";
        quantityController.text = quantity.toString();
      } else {
        isEQ = false;
        _selectedInstrument = 'F&O';
        _selectedProduct = "NRML";
        lotSize = selectedValue!.lotSize;
        lotsController.text = (quantity ~/ lotSize).toString();
        lots = quantity ~/ lotSize;
      }
    }

    stockSearchController.text = widget.positionMetaData!.tradingSymbol;
    actionType = widget.positionMetaData!.transactionType;
  }

  // New method to handle spider data initialization
  void _initializeSpiderData() {
    if (widget.spiderMetaData == null) {
      debugPrint('spiderMetaData is null. Cannot initialize spider data.');
      return;
    }
    BlocProvider.of<WebSocketBloc>(context).add(
      WebSocketSelectStock(
        widget.spiderMetaData!.zenId.toString(),
      ),
    );

    actionType = widget.spiderMetaData!.transactionType;

    // Fetch LTP price
    _fetchLtpPrice(widget.spiderMetaData!.zenId);

    //Accessing the security list to find the spiders zenId
    final secListState = context.read<SecurityListBloc>().state;

    if (secListState is SecurityListLoaded) {
      selectedValue = secListState.equityList.firstWhere(
        (data) => data.zenId == widget.spiderMetaData!.zenId,
        orElse: () => secListState.featuresList.firstWhere(
          (data) => data.zenId == widget.spiderMetaData!.zenId,
          orElse: () {
            debugPrint(
              "🕷️ Data not found in both equityList and featuresList",
            );
            return SecurityModel(
              zenId: 0,
              tradingSymbol: "Not Found",
              strike: 0.0,
              exchanges: ["NSE"],
              lotSize: 1,
              instrumentType: "EQ",
              expiryType: "null",
              expiry: "null",
              name: "Not Found",
            );
          },
        ),
      );

      if (selectedValue != null) {
        debugPrint("🕷️ ${selectedValue.toString()}");
        if (selectedValue!.instrumentType == "EQ") {
          isEQ = true;
          _selectedInstrument = 'EQ';
          _selectedProduct = "CNC";
          quantity = 1;
          quantityController.text = "1";
        } else {
          isEQ = false;
          _selectedInstrument = 'F&O';
          _selectedProduct = "NRML";
          lotSize = selectedValue!.lotSize;
          lotsController.text = '1';
          quantity = lotSize;
          quantityController.text = quantity.toString();
          lots = 1;
        }
      }
    }

    stockSearchController.text = widget.spiderMetaData!.tradingSymbol;
  }

  // New method to fetch LTP price
  Future<void> _fetchLtpPrice(zenId) async {
    try {
      debugPrint("🚀 Fetching LTP");
      final customHttpService = HttpService();
      final authState = context.read<AuthBloc>().state;
      if (authState is AuthAuthenticated) {
        final response = await customHttpService.get(
          Uri.parse(ApiPath.getLtpPrice(
            zenId,
          )),
          headers: {'Content-Type': 'application/json'},
        );
        final jsonData = jsonDecode(response.body);
        final apiResponse = ApiResponse.fromJson(
          jsonData,
          (dynamic payload) => payload,
        );

        if (response.statusCode == 200 &&
            apiResponse.code == 200 &&
            apiResponse.status == 'SUCCESS') {
          final jsonData = apiResponse.payload;
          debugPrint("LTP: $jsonData");
          final ltpPrice = jsonData['latest_price'] as double;
          if (mounted) {
            setState(() {
              price = ltpPrice;
              priceController.text = ltpPrice.toStringAsFixed(2);
              isPriceValid.value = true;
              defaultLtpPrice = ltpPrice;
            });
          }
        }
      }
    } catch (e) {
      debugPrint("Failed to fetch LTP: $e");
    }
  }

  // New method to handle edit data initialization
  void _initializeEditData() {
    orderTitle = widget.latestOrderData!.tradingSymbol;
    isShowBasedOnOrderType = false;
    debugPrint("👽 Edit Order");
    debugPrint("👽 ${widget.latestOrderData!.toString()}");
    quantity = widget.latestOrderData!.quantity;
    quantityController.text = widget.latestOrderData!.quantity.toString();
    isCNC = widget.latestOrderData!.product == "CNC" ? true : false;
    _selectedProduct = widget.latestOrderData!.product;
    actionType = widget.latestOrderData!.transactionType;

    //_fetchLtpPrice(widget.latestOrderData!.positionCompKey.zenSecId);

    //Accessing the security list to find the spiders zenId
    final secListState = context.read<SecurityListBloc>().state;

    if (secListState is SecurityListLoaded) {
      selectedValue = secListState.equityList.firstWhere(
        (data) =>
            data.zenId == widget.latestOrderData!.positionCompKey.zenSecId,
        orElse: () => secListState.featuresList.firstWhere(
          (data) =>
              data.zenId == widget.latestOrderData!.positionCompKey.zenSecId,
          orElse: () {
            debugPrint(
              "🕷️ Data not found in both equityList and featuresList",
            );
            return SecurityModel(
              zenId: 0,
              tradingSymbol: "Security Not Found",
              strike: 0.0,
              exchanges: ["NSE"],
              lotSize: 1,
              instrumentType: "EQ",
              name: "Security Not Found",
              expiryType: "null",
              expiry: "null",
            );
          },
        ),
      );
    }
    if (selectedValue != null) {
      debugPrint(selectedValue.toString());
      if (selectedValue!.instrumentType == "EQ") {
        isEQ = true;
      } else {
        isEQ = false;

        lotSize = selectedValue!.lotSize;

        lotsController.text = (quantity ~/ lotSize).toString();
        lots = quantity ~/ lotSize;
      }
    }
    _initializeEditOrderData();
  }

  void _initializeEditOrderData() {
    // Accessing AuthBloc state
    final authState = context.read<AuthBloc>().state;



    if (authState is AuthAuthenticated) {
      final brokers = authState.credentialsModel.brokers;
      final brokerName = widget.latestOrderData!.positionCompKey.broker;
      final accountId = int.parse(
          widget.latestOrderData!.positionCompKey.accountId.toString());
      final strategyId = int.parse(
          widget.latestOrderData!.positionCompKey.strategyId.toString());

      try {
        final broker = brokers.firstWhere((b) => b.brokerName == brokerName);
        final account =
            broker.accounts.firstWhere((a) => a.accountId == accountId);
        final accountName = account.accountName;
        final strategy =
            account.strategies.firstWhere((s) => s.strategyId == strategyId);
        final strategyName = strategy.strategyName;

        latestAccountName = accountName;
        latestStrategyName = strategyName;
        latestBrokerName = brokerName;
      } catch (e) {
        debugPrint("Error finding broker/account/strategy: $e");
      }
    }

    latestOrderType = OrderFormValidator.reverseValidator(
      widget.latestOrderData!.orderType,
    );

    debugPrint(latestOrderType.toString());
    debugPrint(widget.latestOrderData!.orderType);
    isMarket = latestOrderType!.isMarket;
    isStoplossEnabled = latestOrderType!.isStoplossEnabled;
    isTrailingStoplossEnabled = latestOrderType!.isTrailingStoplossEnabled;
    stopLossType = latestOrderType!.stoplossType;

    price = widget.latestOrderData!.limitPrice;
    priceController.text = widget.latestOrderData!.limitPrice.toString();

    stopLossPrice = widget.latestOrderData!.stopLossLimitPrice;
    stopLossPriceController.text = stopLossPrice!.toString();

    limitPrice = widget.latestOrderData!.limitPrice;
    limitPriceController.text = limitPrice.toString();

    triggerPrice = widget.latestOrderData!.triggerPrice;
    triggerPriceController.text =
        widget.latestOrderData!.triggerPrice.toString();

    BlocProvider.of<WebSocketBloc>(context).add(
      WebSocketSelectStock(
        widget.latestOrderData!.positionCompKey.zenSecId.toString(),
      ),
    );
    debugPrint(
        "Subscribing to WebSocket for ${widget.latestOrderData!.positionCompKey.zenSecId}");
  }

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();
    _stockSelectionFieldFocusNode.dispose();
    //context.read<WebSocketBloc>().add(WebSocketDisconnect());
    debugPrint('Reach !!');
    //BlocProvider.of<WebSocketBloc>().add(WebSocketSelectStock(null));
    //context.read<WebSocketBloc>().add(WebSocketSelectStock(null));
  }

  //this is to adjust the pading based on stock select feild focusing
  double _getDynamicPadding(double keyboardHeight) {
    if (keyboardHeight > 90) {
      return keyboardHeight - 90;
    } else {
      return keyboardHeight;
    }
  }

  //This function is used for handling EQ F&O change
  void _onInstrumentChange(String instrument) {
    setState(() {
      _selectedInstrument = instrument;
      isEQ = instrument == 'EQ';
      isNSE = true;
      selectedValue = null;
      _selectedProduct = (_selectedProduct != "MIS")
          ? (isEQ ? "CNC" : "NRML")
          : _selectedProduct;
      quantity = 0;
      lots = 1;
      stockSearchController.clear();
      quantityController.clear();
      price = null;
      priceController.clear();
      BlocProvider.of<WebSocketBloc>(context).add(WebSocketSelectStock(null));
    });
  }

  //Custom Drop down Searchable i.e Stock Search feild
  void _handleEQSelection() {
    if (quantityController.text.isEmpty) {
      quantity = 1;
      quantityController.text = "1";
      isQuantityValid.value = true;
    }
  }

  void _handleFOSelection(dynamic value) {
    lotSize = value.lotSize;

    if (lotsController.text.isEmpty) {
      lots = 1;
      lotsController.text = "$lots";
    }

    // Update quantity from lots and lotSize
    quantity = lots * lotSize;

    isLotsValid.value = true;
  }

  @override
  Widget build(BuildContext context) {
    AuthBloc authBloc = BlocProvider.of<AuthBloc>(context);
    final authState = authBloc.state;
    double keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    double dynamicWidth = MediaQuery.of(context).size.width * 0.445;
    double dynamicInputWidth = MediaQuery.of(context).size.width * 0.22;

    if (authState is AuthAuthenticated) {
      // If authenticated, retrieve CredentialsModel
      final credentialsModel = authState.credentialsModel;

      return BlocProvider(
        create: (context) =>
            BrokerAccountStrategySelectionBloc(credentialsModel),
        child: Builder(
          builder: (context) {
            return BlocBuilder<ThemeBloc, ThemeState>(
              builder: (context, themeState) {
                return Container(
                  height: MediaQuery.of(context).size.height * 0.85,
                  margin: EdgeInsets.symmetric(horizontal: 2),
                  width: MediaQuery.of(context).size.width *
                      0.98, // 98% of screen width
                  decoration: BoxDecoration(
                    color: AppTheme.surfaceColor(themeState.isDarkMode),
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(20),
                    ),
                  ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //Line bar
                  LineBar(),

                  // Header Row
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 14, horizontal: 15),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Allow Header to take flexible space
                        Header(title: orderTitle),

                        // TogglerWithShadow EQ / FO
                        if (widget.openOrderType == FormOpenOrderType.spider ||
                            widget.openOrderType ==
                                FormOpenOrderType.fromPosition) ...[
                          SizedBox(
                            width: 10,
                          )
                        ] else if (isShowBasedOnOrderType) ...[
                          Flexible(
                            child: TogglerWithShadow(
                              title1: "EQ",
                              title2: "F&O",
                              isEQ: isEQ,
                              action1: () {
                                //Checking to prevent unwanted state updates
                                if (!isEQ) _onInstrumentChange('EQ');
                              },
                              action2: () {
                                //Checking to prevent unwanted state updates
                                if (isEQ) _onInstrumentChange('F&O');
                              },
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  if (widget.openOrderType == FormOpenOrderType.edit) ...[
                    ExchangeSelector2(
                      isNSE: (widget.latestOrderData!.exchange == "NSE" ||
                          widget.latestOrderData!.exchange == "NFO"),
                      defaultPrice: defaultLtpPrice ?? 0.0,
                      action1: (value) =>
                          {}, //for edit order no need to change exchanges
                      action2: (value) => {},
                      selectedSecurity: selectedValue,
                    )
                  ] else ...[
                    ExchangeSelector2(
                      isNSE: isNSE,
                      defaultPrice: defaultLtpPrice ?? 0.0,
                      action1: (value) => setState(() {
                        isNSE = value;
                        selectedExchangeValue = "NSE";
                      }),
                      action2: (value) => setState(() {
                        isNSE = value;
                        selectedExchangeValue = "BSE";
                      }),
                      selectedSecurity: selectedValue,
                    )
                  ],
                  //Adding a scroll view
                  //Starting of scroll container
                  Expanded(
                    flex: 1,
                    child: AnimatedPadding(
                      duration: Duration(milliseconds: 150),
                      curve: Curves.bounceIn,
                      //This is to show the input feild above the mobile keyboard box
                      padding: EdgeInsets.only(
                        bottom: _getDynamicPadding(keyboardHeight),
                      ),
                      child: SingleChildScrollView(
                        child: Form(
                          key: _formKey,
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Market/Limit Toggle
                              StandardMarginWidget(
                                child: MarketLimitToggler(
                                  isMarket: isMarket,
                                  onMarketTap: () {
                                    final isEdit = widget.openOrderType ==
                                        FormOpenOrderType.edit;
                                    // Only allow switching to market if currently NOT market in edit mode
                                    if (isEdit && isMarket) return;
                                    //if edit order and switching to market, disable stoploss
                                    if (isEdit) {
                                      isStoplossEnabled = false;
                                    }
                                    setState(() {
                                      //price = null;
                                      isMarket = true;
                                      //priceController.clear();
                                      isPriceValid.value = true;
                                    });
                                    if (selectedValue != null) {
                                      _fetchLtpPrice(selectedValue!.zenId);
                                    }
                                  },
                                  onLimitTap: () {
                                    final isEdit = widget.openOrderType ==
                                        FormOpenOrderType.edit;
                                    // Allow switching to limit even in edit mode
                                    if (isEdit && isMarket) {
                                      //do nothing in edit mode we cant change market to limit
                                      setState(() => isMarket = false);
                                    } else if (!isEdit) {
                                      setState(() => isMarket = false);
                                    }
                                    // But if already in edit mode + market, prevent switching
                                    // This else block is optional since the button does nothing
                                  },
                                ),
                              ),

                              // Stock Selection avl qty
                              StandardMarginWidget(
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    if (isShowBasedOnOrderType) ...[
                                      CustomSearchableDropdown2(
                                        searchController: stockSearchController,
                                        notifier: isSelectedStockValid,
                                        type: isEQ ? "equity" : "features",
                                        onSelected: (value) async {
                                          debugPrint(
                                              "✅ Selected : ${value.toString()}");

                                          setState(() {
                                            selectedValue = value;

                                            // Reset exchange based on selected exchanges
                                            isNSE = selectedValue!.exchanges
                                                    .contains("NSE") ||
                                                selectedValue!.exchanges
                                                    .contains("NFO");

                                            if (isEQ) {
                                              _handleEQSelection();
                                            } else {
                                              _handleFOSelection(value);
                                            }
                                          });

                                          // Fetch LTP price
                                          _fetchLtpPrice(selectedValue!.zenId);

                                          // Send to WebSocket
                                          BlocProvider.of<WebSocketBloc>(
                                                  context)
                                              .add(WebSocketSelectStock(
                                                  selectedValue!.zenId
                                                      .toString()));
                                        },
                                        onClear: () {
                                          setState(() {
                                            selectedValue = null;
                                            isNSE = true;
                                            price = null;
                                            priceController.clear();
                                          });
                                        },
                                        focusNode:
                                            _stockSelectionFieldFocusNode,
                                      ),
                                    ]

                                    // AvailableQuantityController(
                                    //   action: () {
                                    //     setState(() {
                                    //       viewQuantity = !viewQuantity;
                                    //     });
                                    //   },
                                    // ),
                                  ],
                                ),
                              ),

                              //To list available quantities account wise
                              if (viewQuantity) ...[
                                AccountsWidget(),
                              ],

                              // Quantity and Price Fields
                              // & Product types
                              StandardMarginWidget(
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    // Check if isEQ is true
                                    if (isEQ) ...[
                                      // Show the Qty and Price fields when isEQ is true
                                      InputBox(
                                        placeholder: "Qty.",
                                        action: (value) {
                                          if (value.isNotEmpty) {
                                            // First parse as double, then convert to int
                                            quantity =
                                                double.parse(value).toInt();
                                          } else {
                                            quantity = 0;
                                          }
                                          setState(() {});
                                        },
                                        height: 40,
                                        width: dynamicInputWidth,
                                        controller: quantityController,
                                        notifier: isQuantityValid,
                                        inputFormatter: [
                                          QuantityInputFormatter()
                                        ],
                                      ),
                                      const SizedBox(
                                        width: 6,
                                      ), // Spacing between fields

                                      InputBox(
                                        placeholder: "Price",
                                        isEnabled: !isMarket &&
                                            !isTrailingStoplossEnabled,
                                        action: (value) {
                                          /*
                                          Only digits
                                          Optional decimal with max 2 places (e.g., 123.45)
                                          */
                                          final numValue =
                                              double.tryParse(value);
                                          final isValid = numValue != null &&
                                              numValue > 0 &&
                                              numValue <= 900000 &&
                                              RegExp(r'^\d+(\.\d{1,2})?$')
                                                  .hasMatch(value);
                                          if (isValid) {
                                            price = numValue;
                                          } else {
                                            price = null;
                                            isPriceValid.value = false;
                                          }
                                          setState(() {});
                                          isPriceValid.value = isValid;
                                        },
                                        height: 40,
                                        width: dynamicInputWidth,
                                        controller: priceController,
                                        notifier: isPriceValid,
                                        inputFormatter: [PriceInputFormatter()],
                                      ),
                                    ] else ...[
                                      // Show a single field for lots input when isEQ is false
                                      InputBox(
                                        placeholder:
                                            "Lot (Qty.${quantity == 0 ? "--" : quantity})",
                                        placeHolderFontSize: 12,
                                        action: (value) {
                                          if (value.isNotEmpty) {
                                            lots = int.parse(value);
                                          } else {
                                            lots = 1;
                                          }
                                          quantity = lots * lotSize;
                                          setState(() {});
                                        },
                                        height: 40,
                                        width: 95,
                                        controller: lotsController,
                                        notifier: isLotsValid,
                                        inputFormatter: [
                                          QuantityInputFormatter()
                                        ],
                                      ),
                                      const SizedBox(
                                        width: 4,
                                      ),
                                      //if (!isTrailingStoplossEnabled)
                                      InputBox(
                                        placeholder: "Price",
                                        isEnabled: !isMarket &&
                                            !isTrailingStoplossEnabled,
                                        action: (value) {
                                          /*
                                          Only digits
                                          Optional decimal with max 2 places (e.g., 123.45)
                                          */
                                          final numValue =
                                              double.tryParse(value);
                                          final isValid = numValue != null &&
                                              numValue > 0 &&
                                              numValue <= 900000 &&
                                              RegExp(r'^\d+(\.\d{1,2})?$')
                                                  .hasMatch(value);

                                          if (isValid) {
                                            price = numValue;
                                          } else {
                                            price = null;
                                          }
                                          setState(() {});
                                          isPriceValid.value = isValid;
                                        },
                                        height: 40,
                                        width: dynamicInputWidth,
                                        controller: priceController,
                                        notifier: isPriceValid,
                                      )
                                    ],
                                    SizedBox(
                                      width: 2,
                                    ),

                                    // The radio buttons NRML CNC MIS
                                    if (isShowBasedOnOrderType)
                                      Flexible(
                                        child: Row(
                                          children: _productTypes[
                                                  _selectedInstrument]!
                                              .map(
                                                (product) => Row(
                                                  children: [
                                                    CustomRadioWidget(
                                                      groupValue:
                                                          _selectedProduct,
                                                      value: product,
                                                      onChanged: (value) {
                                                        setState(() {
                                                          _selectedProduct =
                                                              value;
                                                        });
                                                      },
                                                      height: 15,
                                                      width: 15,
                                                    ),
                                                    Text(
                                                      product,
                                                      style: TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 15,
                                                        fontWeight:
                                                            FontWeight.w400,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              )
                                              .toList(),
                                        ),
                                      )
                                    else
                                      SizedBox(
                                        width: 170,
                                      ),
                                  ],
                                ),
                              ),
                              //ShadedInputField(),

                              // Stoploss Toggle
                              StandardMarginWidget(
                                  child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      spacing: 2,
                                      children: [
                                    //Stop loss toggler info
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment
                                          .start, // Align widgets to the start
                                      crossAxisAlignment: CrossAxisAlignment
                                          .center, // Center widgets vertically
                                      children: [
                                        Text(
                                          'Stoploss',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 16,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        Transform.scale(
                                          scale: 0.5,
                                          child: Switch(
                                            activeTrackColor: Colors.white,
                                            inactiveTrackColor:
                                                Colors.transparent,
                                            inactiveThumbColor: Colors.white,
                                            value: isStoplossEnabled,
                                            onChanged: (value) {
                                              setState(() {
                                                isStoplossEnabled = value;
                                                isTrailingStoplossEnabled =
                                                    false;
                                              });
                                            },
                                            activeColor: Colors.black,
                                          ),
                                        ),
                                        // Icon(
                                        //   size: 18,
                                        //   Icons.info_outline,
                                        //   color: Colors.blue,
                                        // ),
                                        SizedBox(
                                          width: 3,
                                        )
                                      ],
                                    ),

                                    if (isStoplossEnabled) ...[
                                      //Market Limit
                                      MLToggler(
                                        selectedType: stopLossType,
                                        onTypeChanged: (val) =>
                                            setState(() => stopLossType = val),
                                        isEnabled: widget.openOrderType ==
                                                FormOpenOrderType.edit
                                            ? false
                                            : true,
                                      ),
                                      //Trailing
                                      // Row(
                                      //   mainAxisAlignment:
                                      //       MainAxisAlignment.start,
                                      //   crossAxisAlignment:
                                      //       CrossAxisAlignment.center,
                                      //   children: [
                                      //     CustomRadioWidget(
                                      //       isDisabled: widget.openOrderType ==
                                      //               FormOpenOrderType.edit
                                      //           ? true
                                      //           : false,
                                      //       groupValue: true,
                                      //       value: isTrailingStoplossEnabled,
                                      //       onChanged: (value) {
                                      //         setState(() {
                                      //           isTrailingStoplossEnabled =
                                      //               !value;
                                      //         });
                                      //       },
                                      //       height: 15,
                                      //       width: 15,
                                      //     ),
                                      //     Text(
                                      //       "Trailing",
                                      //       style: TextStyle(
                                      //         color: Colors.white,
                                      //         fontSize: 14,
                                      //         fontWeight: FontWeight.w400,
                                      //       ),
                                      //     ),
                                      //   ],
                                      // ),
                                    ]
                                  ])),

                              //Trigger price and stop loss Limit price
                              //Activation points
                              // Trailing Stop Loss Enabled
                              if (isTrailingStoplossEnabled) ...[
                                StandardMarginWidget(
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      // Activation Points (always shown when trailing is enabled)
                                      InputBox(
                                        height: 40,
                                        width: dynamicWidth,
                                        placeholder: "Activation points",
                                        action: (value) {
                                          setState(() {
                                            activationPoints = value.isNotEmpty
                                                ? double.parse(value)
                                                : null;
                                          });
                                        },
                                        controller: activationPointsController,
                                        notifier: isActivationPointsValid,
                                      ),
                                      // Limit Price (only shown when type is "L")
                                      if (stopLossType == "L")
                                        InputBox(
                                          height: 40,
                                          width: dynamicWidth,
                                          placeholder: "Limit Price",
                                          action: (value) {
                                            setState(() {
                                              price = value.isNotEmpty
                                                  ? double.parse(value)
                                                  : null;
                                            });
                                          },
                                          controller: limitPriceController,
                                          notifier: isLimitPriceValid,
                                        ),
                                      // Trigger Price (only shown when type is "M")
                                      if (stopLossType == "M")
                                        InputBox(
                                          height: 40,
                                          width: dynamicWidth,
                                          placeholder: "Trigger price",
                                          action: (value) {
                                            setState(() {
                                              triggerPrice = value.isNotEmpty
                                                  ? double.parse(value)
                                                  : null;
                                            });
                                          },
                                          isEnabled: widget.openOrderType ==
                                                  FormOpenOrderType.edit
                                              ? false
                                              : true,
                                          controller: triggerPriceController,
                                          notifier: isTriggerPriceValid,
                                        ),
                                    ],
                                  ),
                                ),
                                // Second Row for Activation Points when type is "L"
                                if (stopLossType == "L")
                                  StandardMarginWidget(
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        InputBox(
                                          height: 40,
                                          width: dynamicWidth,
                                          placeholder: "Trigger price",
                                          action: (value) {
                                            setState(() {
                                              triggerPrice = value.isNotEmpty
                                                  ? double.parse(value)
                                                  : null;
                                            });
                                          },
                                          isEnabled: widget.openOrderType ==
                                                  FormOpenOrderType.edit
                                              ? false
                                              : true,
                                          controller: triggerPriceController,
                                          notifier: isTriggerPriceValid,
                                        ),
                                      ],
                                    ),
                                  ),
                              ],

                              // Trailing Stop Loss Disabled, but Stop Loss Enabled
                              if (!isTrailingStoplossEnabled &&
                                  isStoplossEnabled) ...[
                                StandardMarginWidget(
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      // Trigger Price (always shown when stop loss is enabled)
                                      InputBox(
                                        height: 40,
                                        width: dynamicWidth,
                                        placeholder: "Trigger price",
                                        action: (value) {
                                          setState(() {
                                            triggerPrice = value.isNotEmpty
                                                ? double.parse(value)
                                                : null;
                                          });
                                        },
                                        // isEnabled: widget.openOrderType ==
                                        //         FormOpenOrderType.edit && !isMarket
                                        //     ? true
                                        //     : false,
                                        controller: triggerPriceController,
                                        notifier: isTriggerPriceValid,
                                      ),
                                      // Stop Loss Price (only shown when type is "L")
                                      if (stopLossType == "L")
                                        InputBox(
                                          height: 40,
                                          width: dynamicWidth,
                                          placeholder: "Stoploss price",
                                          action: (value) {
                                            setState(() {
                                              stopLossPrice = value.isNotEmpty
                                                  ? double.parse(value)
                                                  : null;
                                            });
                                          },
                                          // isEnabled: widget.openOrderType ==
                                          //         FormOpenOrderType.edit && !isMarket
                                          //     ? true
                                          //     : false,
                                          controller: stopLossPriceController,
                                          notifier: isStoplossPricePriceValid,
                                        ),
                                    ],
                                  ),
                                ),
                              ],

                              //Broker Account
                              //Create
                              if (isExpanded &&
                                  (widget.openOrderType ==
                                          FormOpenOrderType.create ||
                                      widget.openOrderType ==
                                          FormOpenOrderType.spider ||
                                      widget.openOrderType ==
                                          FormOpenOrderType.fromPosition ||
                                      widget.openOrderType ==
                                          FormOpenOrderType.buy ||
                                      widget.openOrderType ==
                                          FormOpenOrderType.sell)) ...[
                                StandardMarginWidget(
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      BlocBuilder<
                                          BrokerAccountStrategySelectionBloc,
                                          BrokerAccountStrategyState>(
                                        builder: (context, state) {
                                          return SelectionDropdown<BrokerInfo>(
                                            hint: "Broker",
                                            selectedValue: state.selectedBroker,
                                            items: state.brokers,
                                            getItemLabel: (broker) =>
                                                broker.brokerName,
                                            onChanged: (broker) {
                                              if (broker != null) {
                                                setState(() {});
                                                context
                                                    .read<
                                                        BrokerAccountStrategySelectionBloc>()
                                                    .add(
                                                        BrokerSelected(broker));
                                              }
                                            },
                                          );
                                        },
                                      ),
                                      BlocBuilder<
                                          BrokerAccountStrategySelectionBloc,
                                          BrokerAccountStrategyState>(
                                        builder: (context, state) {
                                          return SelectionDropdown<AccountInfo>(
                                            hint: "Account",
                                            selectedValue: state
                                                    .availableAccounts
                                                    .contains(
                                                        state.selectedAccount)
                                                ? state.selectedAccount
                                                : null,
                                            items: state.availableAccounts,
                                            getItemLabel: (account) =>
                                                account.accountName,
                                            onChanged: (account) {
                                              if (account != null) {
                                                setState(() {});
                                                context
                                                    .read<
                                                        BrokerAccountStrategySelectionBloc>()
                                                    .add(AccountSelected(
                                                        account));
                                              }
                                            },
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                                StandardMarginWidget(
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      BlocBuilder<
                                          BrokerAccountStrategySelectionBloc,
                                          BrokerAccountStrategyState>(
                                        builder: (context, state) {
                                          return SelectionDropdown<Strategy>(
                                            hint: "Strategy",
                                            selectedValue: state
                                                    .availableStrategies
                                                    .contains(
                                                        state.selectedStrategy)
                                                ? state.selectedStrategy
                                                : null,
                                            items: state.availableStrategies,
                                            getItemLabel: (strategy) =>
                                                strategy.strategyName,
                                            onChanged: (strategy) {
                                              debugPrint(
                                                  "Strategy selection Reach");
                                              setState(() {});
                                              if (strategy != null) {
                                                context
                                                    .read<
                                                        BrokerAccountStrategySelectionBloc>()
                                                    .add(StrategySelected(
                                                        strategy));
                                              }
                                            },
                                          );
                                        },
                                      ),
                                      OrferFormMarginViewer()
                                    ],
                                  ),
                                ),
                                // const StandardMarginWidget(
                                //     child:  OrferFormMarginViewer())
                              ],

                              //Edit to show only read only B S A
                              if (isExpanded &&
                                  (widget.openOrderType ==
                                      FormOpenOrderType.edit)) ...[
                                StandardMarginWidget(
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      DisabledDropdown(value: latestBrokerName),
                                      DisabledDropdown(
                                          value: latestAccountName),
                                    ],
                                  ),
                                ),
                                StandardMarginWidget(
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      DisabledDropdown(
                                          value: latestStrategyName),
                                      OrferFormMarginViewer(
                                        accountId: widget.latestOrderData!
                                            .positionCompKey.accountId,
                                      )
                                    ],
                                  ),
                                ),
                                // StandardMarginWidget(
                                //     child: OrferFormMarginViewer())
                              ],

                              //Expand viewer icon
                              GestureDetector(
                                behavior: HitTestBehavior.opaque,
                                onTap: () {
                                  setState(() {
                                    isExpanded = !isExpanded;
                                  });
                                  if (_controller.isDismissed) {
                                    _controller.forward();
                                  } else {
                                    _controller.reverse();
                                  }
                                },
                                child: Container(
                                  margin: EdgeInsets.only(top: 16),
                                  alignment: Alignment.center,
                                  //width: 24,
                                  height: 24,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    spacing: 4,
                                    children: [
                                      ThemeConstants.zenText(
                                        isExpanded ? "Less" : "More",
                                      ),
                                      RotationTransition(
                                        turns: _animation,
                                        child: ImageIcon(
                                          AssetImage(
                                              "images/expand-for-more.png"),
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  //Ending of scroll container

                  // Amount & Charges Bar
                  // Container(
                  //   height: 32,
                  //   decoration: BoxDecoration(
                  //       color: Color(0xffD9D9D9),
                  //       borderRadius: BorderRadius.vertical(
                  //         top: Radius.circular(10),
                  //       )),
                  //   child: Row(
                  //     mainAxisAlignment: MainAxisAlignment.spaceAround,
                  //     crossAxisAlignment: CrossAxisAlignment.center,
                  //     children: [
                  //       Text(
                  //         'Amount',
                  //         style: TextStyle(
                  //           color: Colors.black,
                  //         ),
                  //       ),
                  //       BlocBuilder<WebSocketBloc, WebSocketState>(
                  //         builder: (context, state) {
                  //           if (state is WebSocketStockPriceUpdated &&
                  //               isMarket) {
                  //             if (state.stockPrice != null) {
                  //               double total = state.stockPrice! * quantity;
                  //               if (state.stockPrice != 0) {
                  //                 return Text(
                  //                   '₹ ${UtilFunctions.formatIndianCurrency(total)}',
                  //                   style: TextStyle(
                  //                     color: Colors.green,
                  //                   ),
                  //                 );
                  //               }
                  //             }
                  //           }

                  //           if (price != 0 && price != null) {
                  //             double total = price! * quantity;
                  //             return Text(
                  //               '₹ ${UtilFunctions.formatIndianCurrency(total)}',
                  //               style: TextStyle(
                  //                 color: Colors.green,
                  //               ),
                  //             );
                  //           }
                  //           return Text(' --- ');
                  //         },
                  //       ),
                  //       Text(
                  //         'Charges',
                  //         style: TextStyle(
                  //           color: Colors.black,
                  //         ),
                  //       ),
                  //       Text(
                  //         '₹0.00',
                  //         style: TextStyle(
                  //           color: Colors.green,
                  //         ),
                  //       ),
                  //       SpinningIconButton(
                  //         controller: _formRefreshController,
                  //         iconData: Icons.refresh,
                  //         onPressed: () {
                  //           _formRefreshController.repeat();
                  //           _formRefreshController.forward(
                  //             from: _formRefreshController.value,
                  //           );
                  //           //only refresh if it is market
                  //           if (selectedValue != null && isMarket) {
                  //             _fetchLtpPrice(selectedValue!.zenId);
                  //           }
                  //         },
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  ChargesBar(
                    price: price,
                    quantity: quantity,
                    selectedValue: selectedValue,
                    isMarket: isMarket,
                    formRefreshController: _formRefreshController,
                    fetchLtpPrice: _fetchLtpPrice,
                    exchange: selectedExchangeValue,
                    productType: _selectedProduct,
                    broker: selectedBroker,
                    transactionType: actionType,
                  ),

                  /// BUY SELL buttons
                  Container(
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(
                          color: Colors.grey.withOpacity(0.2),
                        ),
                      ),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            if (widget.openOrderType ==
                                FormOpenOrderType.create) ...[
                              Expanded(
                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    padding: EdgeInsets.symmetric(vertical: 14),
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8)),
                                  ),
                                  onPressed: () {
                                    buyAction(context, credentialsModel);
                                  },
                                  child: Text(
                                    'Buy',
                                    style: TextStyle(
                                      color: ThemeConstants.zenBlack,
                                      fontSize: 20,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    padding: EdgeInsets.symmetric(vertical: 14),
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8)),
                                  ),
                                  onPressed: () {
                                    sellAction(context, credentialsModel);
                                  },
                                  child: Text(
                                    'Sell',
                                    style: TextStyle(
                                      color: ThemeConstants.zenBlack,
                                      fontSize: 20,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            ] else if (widget.openOrderType ==
                                FormOpenOrderType.buy) ...[
                              Expanded(
                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    padding: EdgeInsets.symmetric(vertical: 14),
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8)),
                                  ),
                                  onPressed: () {
                                    buyAction(context, credentialsModel);
                                  },
                                  child: Text(
                                    'Buy',
                                    style: TextStyle(
                                      color: ThemeConstants.zenBlack,
                                      fontSize: 20,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            ] else if (widget.openOrderType ==
                                FormOpenOrderType.sell) ...[
                              Expanded(
                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    padding: EdgeInsets.symmetric(vertical: 14),
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8)),
                                  ),
                                  onPressed: () {
                                    sellAction(context, credentialsModel);
                                  },
                                  child: Text(
                                    'Sell',
                                    style: TextStyle(
                                      color: ThemeConstants.zenBlack,
                                      fontSize: 20,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            ] else if (widget.openOrderType ==
                                FormOpenOrderType.edit) ...[
                              Expanded(
                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: ThemeConstants.blue,
                                    padding: EdgeInsets.symmetric(vertical: 14),
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8)),
                                  ),
                                  onPressed: () {
                                    modifyAction(context);
                                  },
                                  child: Text(
                                    'Modify',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 20,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            ] else if (widget.openOrderType ==
                                    FormOpenOrderType.spider ||
                                widget.openOrderType ==
                                    FormOpenOrderType.fromPosition) ...[
                              if (widget.openOrderType ==
                                      FormOpenOrderType.spider
                                  ? widget.spiderMetaData?.transactionType ==
                                      "BUY"
                                  : widget.positionMetaData?.transactionType ==
                                      "BUY")
                                Expanded(
                                  child: ElevatedButton(
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green,
                                      padding:
                                          EdgeInsets.symmetric(vertical: 14),
                                      shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(8)),
                                    ),
                                    onPressed: () {
                                      buyAction(context, credentialsModel);
                                    },
                                    child: Text(
                                      'Buy',
                                      style: TextStyle(
                                        color: ThemeConstants.zenBlack,
                                        fontSize: 20,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                )
                              else
                                Expanded(
                                  child: ElevatedButton(
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.red,
                                      padding:
                                          EdgeInsets.symmetric(vertical: 14),
                                      shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(8)),
                                    ),
                                    onPressed: () {
                                      sellAction(context, credentialsModel);
                                    },
                                    child: Text(
                                      'Sell',
                                      style: TextStyle(
                                        color: ThemeConstants.zenBlack,
                                        fontSize: 20,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                            ] else if (widget.openOrderType ==
                                FormOpenOrderType.delete) ...[
                              Expanded(
                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor:
                                        ThemeConstants.titleRedColor,
                                    padding: EdgeInsets.symmetric(vertical: 14),
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8)),
                                  ),
                                  onPressed: () {
                                    deleteAction(context);
                                  },
                                  child: Text(
                                    'Delete',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 20,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            ] else ...[
                              Expanded(
                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    padding: EdgeInsets.symmetric(vertical: 14),
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8)),
                                  ),
                                  onPressed: () {
                                    buyAction(context, credentialsModel);
                                  },
                                  child: Text(
                                    'Buy',
                                    style: TextStyle(
                                      color: ThemeConstants.zenBlack,
                                      fontSize: 20,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    padding: EdgeInsets.symmetric(vertical: 14),
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8)),
                                  ),
                                  onPressed: () {
                                    sellAction(context, credentialsModel);
                                  },
                                  child: Text(
                                    'Sell',
                                    style: TextStyle(
                                      color: ThemeConstants.zenBlack,
                                      fontSize: 20,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            ]
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
          }
        
        )

        );
        
      
    } else {
      return Scaffold(
        body: Center(child: Text('Please log in to proceed')),
      );
    }
  }

  void modifyAction(context) {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    OrderFormModel data = OrderFormModel(
      clientId: widget.latestOrderData!.positionCompKey.clientId,
      accountId: widget.latestOrderData!.positionCompKey.accountId,
      strategyId: widget.latestOrderData!.positionCompKey.strategyId,
      broker: widget.latestOrderData!.positionCompKey.broker,
      exchange: widget.latestOrderData!.exchange,
      transactionType: widget.latestOrderData!.transactionType,
      quantity: quantity,
      product: widget.latestOrderData!.product,
      validity: widget.latestOrderData!.validity,
      methodType: "PUT",
      zenId: widget.latestOrderData!.positionCompKey.zenSecId,
      zenOrderId: widget.latestOrderData!.zenOrderId,
      isMarket: isMarket,
      isStoplossEnabled: isStoplossEnabled,
      isTrailingStoplossEnabled: isTrailingStoplossEnabled,
      stoplossType: stopLossType,
      //for handling the input feilds
      limitPrice:
          isTrailingStoplossEnabled && stopLossType == "L" ? limitPrice : price,
      triggerPrice: triggerPrice,
      stopLossLimitPrice: stopLossPrice,
      activationPoints: activationPoints,
    );

    //returns he order type
    String orderType = OrderFormValidator.validator(data);

    data.orderType = orderType;

    debugPrint(widget.latestOrderData!.orderType);
    debugPrint(orderType);
    debugPrint(data.toString());

    BlocProvider.of<OrdersBloc>(context).add(PlaceOrderEvent(data));
    BlocProvider.of<WebSocketBloc>(context).add(WebSocketSelectStock(null));
  }

  void deleteAction(context) {
    OrderFormModel data = OrderFormModel(
      clientId: widget.latestOrderData!.positionCompKey.clientId,
      accountId: widget.latestOrderData!.positionCompKey.accountId,
      strategyId: widget.latestOrderData!.positionCompKey.strategyId,
      broker: widget.latestOrderData!.positionCompKey.broker,
      exchange: widget.latestOrderData!.exchange,
      transactionType: widget.latestOrderData!.transactionType,
      quantity: quantity,
      product: widget.latestOrderData!.product,
      validity: widget.latestOrderData!.validity,
      methodType: "DELETE",
      zenId: widget.latestOrderData!.positionCompKey.zenSecId,
      zenOrderId: widget.latestOrderData!.zenOrderId,
      isMarket: isMarket,
      isStoplossEnabled: isStoplossEnabled,
      isTrailingStoplossEnabled: isTrailingStoplossEnabled,
      stoplossType: stopLossType,
      //for handling the input feilds
      limitPrice:
          isTrailingStoplossEnabled && stopLossType == "L" ? limitPrice : price,
      triggerPrice: triggerPrice,
      stopLossLimitPrice: stopLossPrice,
      activationPoints: activationPoints,
    );

    //returns he order type
    String orderType = OrderFormValidator.validator(data);

    data.orderType = orderType;

    debugPrint(widget.latestOrderData!.orderType);
    debugPrint(orderType);
    debugPrint(data.toString());

    BlocProvider.of<OrdersBloc>(context).add(PlaceOrderEvent(data));
    BlocProvider.of<WebSocketBloc>(context).add(WebSocketSelectStock(null));
  }

  void buyAction(context, CredentialsModel credentialsModel) {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (selectedValue == null) {
      return;
    }

    BrokerAccountStrategySelectionBloc selectionBloC =
        BlocProvider.of<BrokerAccountStrategySelectionBloc>(context);
    final selectionState = selectionBloC.state;

    OrderFormModel data = OrderFormModel(
      clientId: credentialsModel.clientId,
      accountId: selectionState.selectedAccount?.accountId ?? 0,
      strategyId: selectionState.selectedStrategy?.strategyId ?? 0,
      broker: selectionState.selectedBroker?.brokerName ?? "",
      exchange: isNSE
          ? isEQ
              ? "NSE"
              : "NFO"
          : "BSE",
      transactionType: "BUY",
      quantity: quantity,
      product: _selectedProduct,
      validity: "DAY",
      methodType: "POST",
      zenId: selectedValue!.zenId,
      isMarket: isMarket,
      isStoplossEnabled: isStoplossEnabled,
      isTrailingStoplossEnabled: isTrailingStoplossEnabled,
      stoplossType: stopLossType,
      //for handling the input feilds
      limitPrice:
          isTrailingStoplossEnabled && stopLossType == "L" ? limitPrice : price,
      triggerPrice: triggerPrice,
      stopLossLimitPrice: stopLossPrice,
      activationPoints: activationPoints,
    );

    //returns he order type
    String orderType = OrderFormValidator.validator(data);

    data.orderType = orderType;

    debugPrint("🏛️  BUY ACTION :\n ${data.toString()}");

    BlocProvider.of<OrdersBloc>(context).add(PlaceOrderEvent(data));
    BlocProvider.of<WebSocketBloc>(context).add(WebSocketSelectStock(null));
    hideOrderForm(context);
  }

  void sellAction(context, CredentialsModel credentialsModel) {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (selectedValue == null) {
      return;
    }

    BrokerAccountStrategySelectionBloc selectionBloC =
        BlocProvider.of<BrokerAccountStrategySelectionBloc>(context);
    final selectionState = selectionBloC.state;

    OrderFormModel data = OrderFormModel(
      clientId: credentialsModel.clientId,
      accountId: selectionState.selectedAccount?.accountId ?? 0,
      strategyId: selectionState.selectedStrategy?.strategyId ?? 0,
      broker: selectionState.selectedBroker?.brokerName ?? "",
      exchange: isNSE
          ? isEQ
              ? "NSE"
              : "NFO"
          : "BSE",
      transactionType: "SELL",
      quantity: quantity,
      product: _selectedProduct,
      validity: "DAY",
      methodType: "POST",
      zenId: selectedValue!.zenId,
      isMarket: isMarket,
      isStoplossEnabled: isStoplossEnabled,
      isTrailingStoplossEnabled: isTrailingStoplossEnabled,
      stoplossType: stopLossType,
      //for handling the input feilds
      limitPrice:
          isTrailingStoplossEnabled && stopLossType == "L" ? limitPrice : price,
      triggerPrice: triggerPrice,
      stopLossLimitPrice: stopLossPrice,
      activationPoints: activationPoints,
    );

    String orderType = OrderFormValidator.validator(data);

    data.orderType = orderType;

    debugPrint("🏛️  SELL ACTION :\n ${data.toString()}");

    BlocProvider.of<OrdersBloc>(context).add(PlaceOrderEvent(data));
    BlocProvider.of<WebSocketBloc>(context).add(WebSocketSelectStock(null));
    hideOrderForm(context);
  }
}

//availabe quantity holder
class AvailableQuantityController extends StatelessWidget {
  final Function action;

  const AvailableQuantityController({
    super.key,
    required this.action,
  });

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          action();
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          spacing: 2,
          children: [
            ImageIcon(
              AssetImage("images/qty-case.png"),
              color: Colors.white,
              size: 18,
            ),
            Text(
              "1200",
              style: TextStyle(color: Colors.white, fontSize: 10),
            ),
            Text(
              "(Availabe Qty.)",
              style: TextStyle(color: Colors.white, fontSize: 10),
            ),
            ImageIcon(
              AssetImage("images/expand-for-more.png"),
              color: Colors.white,
              size: 18,
            ),
          ],
        ),
      ),
    );
  }
}

//margin - ui
class StandardMarginWidget extends StatelessWidget {
  final Widget child;
  const StandardMarginWidget({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
          horizontal: 14, vertical: 8), // Standard margin
      child: child,
    );
  }
}
